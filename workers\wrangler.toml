#:schema node_modules/wrangler/config-schema.json
name = "gupiao-zijinliu-api"
main = "src/index.ts"
compatibility_date = "2023-12-18"
compatibility_flags = ["nodejs_compat"]

# KV命名空间配置
# 注意：这些ID需要在Cloudflare Dashboard中创建KV命名空间后替换
[[kv_namespaces]]
binding = "STOCK_CACHE"
id = "02039488cd804d9c970cf16057aa5150"
preview_id = "08838abb8a404a6e9e4ce0e637932525"

[[kv_namespaces]]
binding = "STOCK_CONFIG"
id = "735300a34b2b4acca4835fdb1835783a"
preview_id = "004ca55a3ce94d5abc86bf21f8ae4430"

# 定时任务配置 - 每分钟执行一次
[triggers]
crons = ["* * * * *"]

# 环境变量
[vars]
ENVIRONMENT = "development"
API_BASE_URL = "https://push2.eastmoney.com"
CRON_ENABLED = "true"
CACHE_TTL = "60"
BATCH_SIZE = "10"
MAX_RETRIES = "3"
LOG_LEVEL = "info"

# 开发环境配置
[env.development]
name = "gupiao-zijinliu-api-dev"

[env.development.vars]
ENVIRONMENT = "development"
API_BASE_URL = "https://push2.eastmoney.com"
CRON_ENABLED = "true"
CACHE_TTL = "60"
BATCH_SIZE = "5"
MAX_RETRIES = "3"
LOG_LEVEL = "debug"

# 生产环境配置
[env.production]
name = "gupiao-zijinliu-api-prod"

# 生产环境KV命名空间
[[env.production.kv_namespaces]]
binding = "STOCK_CACHE"
id = "02039488cd804d9c970cf16057aa5150"

[[env.production.kv_namespaces]]
binding = "STOCK_CONFIG"
id = "735300a34b2b4acca4835fdb1835783a"

[env.production.vars]
ENVIRONMENT = "production"
API_BASE_URL = "https://push2.eastmoney.com"
CRON_ENABLED = "true"
CACHE_TTL = "60"
BATCH_SIZE = "10"
MAX_RETRIES = "3"
LOG_LEVEL = "info"
